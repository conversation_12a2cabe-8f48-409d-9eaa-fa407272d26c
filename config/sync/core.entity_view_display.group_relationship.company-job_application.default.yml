uuid: b1edeedd-2376-4344-9ddd-6ea60b237b49
langcode: en
status: true
dependencies:
  config:
    - field.field.group_relationship.company-job_application.field_certificates
    - field.field.group_relationship.company-job_application.field_certificates_new
    - field.field.group_relationship.company-job_application.field_cv
    - field.field.group_relationship.company-job_application.field_cv_new
    - field.field.group_relationship.company-job_application.field_education
    - field.field.group_relationship.company-job_application.field_email
    - field.field.group_relationship.company-job_application.field_first_name
    - field.field.group_relationship.company-job_application.field_interested_city
    - field.field.group_relationship.company-job_application.field_job_post
    - field.field.group_relationship.company-job_application.field_languages
    - field.field.group_relationship.company-job_application.field_last_name
    - field.field.group_relationship.company-job_application.field_linkedin_profile
    - field.field.group_relationship.company-job_application.field_message
    - field.field.group_relationship.company-job_application.field_phone_number
    - field.field.group_relationship.company-job_application.field_photo
    - field.field.group_relationship.company-job_application.field_professional_field
    - field.field.group_relationship.company-job_application.field_professional_level
    - field.field.group_relationship.company-job_application.field_reference_number
    - field.field.group_relationship.company-job_application.field_residence_place
    - field.field.group_relationship.company-job_application.field_return_reason
    - field.field.group_relationship.company-job_application.field_status
    - group.relationship_type.company-job_application
    - responsive_image.styles.talent
  module:
    - file
    - link
    - options
    - phone_number
    - responsive_image
id: group_relationship.company-job_application.default
targetEntityType: group_relationship
bundle: company-job_application
mode: default
content:
  field_certificates:
    type: file_default
    label: above
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 18
    region: content
  field_certificates_new:
    type: file_default
    label: above
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 19
    region: content
  field_cv:
    type: file_default
    label: above
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 16
    region: content
  field_cv_new:
    type: file_default
    label: above
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 17
    region: content
  field_education:
    type: entity_reference_label
    label: above
    settings:
      link: false
    third_party_settings: {  }
    weight: 4
    region: content
  field_email:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 12
    region: content
  field_first_name:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 7
    region: content
  field_interested_city:
    type: entity_reference_label
    label: above
    settings:
      link: false
    third_party_settings: {  }
    weight: 9
    region: content
  field_job_post:
    type: entity_reference_label
    label: hidden
    settings:
      link: false
    third_party_settings: {  }
    weight: 0
    region: content
  field_languages:
    type: entity_reference_label
    label: above
    settings:
      link: false
    third_party_settings: {  }
    weight: 15
    region: content
  field_last_name:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 8
    region: content
  field_linkedin_profile:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 14
    region: content
  field_message:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 20
    region: content
  field_phone_number:
    type: phone_number_international
    label: hidden
    settings:
      as_link: false
    third_party_settings: {  }
    weight: 13
    region: content
  field_photo:
    type: responsive_image
    label: hidden
    settings:
      responsive_image_style: talent
      image_link: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 3
    region: content
  field_professional_field:
    type: entity_reference_label
    label: above
    settings:
      link: false
    third_party_settings: {  }
    weight: 5
    region: content
  field_professional_level:
    type: entity_reference_label
    label: above
    settings:
      link: false
    third_party_settings: {  }
    weight: 6
    region: content
  field_reference_number:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_residence_place:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 10
    region: content
  field_return_reason:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 11
    region: content
  field_status:
    type: list_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
hidden:
  entity_id: true
  langcode: true
  search_api_attachments: true
  search_api_excerpt: true
  uid: true
