uuid: 0a06bc33-9d48-46a4-a40b-33e6d2714704
langcode: en
status: true
dependencies:
  config:
    - field.field.group_relationship.company-job_application.field_certificates
    - field.field.group_relationship.company-job_application.field_certificates_new
    - field.field.group_relationship.company-job_application.field_cv
    - field.field.group_relationship.company-job_application.field_cv_new
    - field.field.group_relationship.company-job_application.field_education
    - field.field.group_relationship.company-job_application.field_email
    - field.field.group_relationship.company-job_application.field_first_name
    - field.field.group_relationship.company-job_application.field_interested_city
    - field.field.group_relationship.company-job_application.field_job_post
    - field.field.group_relationship.company-job_application.field_languages
    - field.field.group_relationship.company-job_application.field_last_name
    - field.field.group_relationship.company-job_application.field_linkedin_profile
    - field.field.group_relationship.company-job_application.field_message
    - field.field.group_relationship.company-job_application.field_phone_number
    - field.field.group_relationship.company-job_application.field_photo
    - field.field.group_relationship.company-job_application.field_professional_field
    - field.field.group_relationship.company-job_application.field_professional_level
    - field.field.group_relationship.company-job_application.field_reference_number
    - field.field.group_relationship.company-job_application.field_residence_place
    - field.field.group_relationship.company-job_application.field_return_reason
    - field.field.group_relationship.company-job_application.field_status
    - group.relationship_type.company-job_application
    - image.style.thumbnail
  module:
    - file
    - image
    - link
    - path
    - phone_number
id: group_relationship.company-job_application.default
targetEntityType: group_relationship
bundle: company-job_application
mode: default
content:
  entity_id:
    type: entity_reference_autocomplete
    weight: 0
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_certificates:
    type: file_generic
    weight: 13
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_certificates_new:
    type: file_generic
    weight: 16
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_cv:
    type: file_generic
    weight: 12
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_cv_new:
    type: file_generic
    weight: 15
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_education:
    type: entity_reference_autocomplete
    weight: 11
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_email:
    type: string_textfield
    weight: 4
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_first_name:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_interested_city:
    type: entity_reference_autocomplete
    weight: 21
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_job_post:
    type: entity_reference_autocomplete
    weight: 7
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_languages:
    type: entity_reference_autocomplete
    weight: 24
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_last_name:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_linkedin_profile:
    type: link_default
    weight: 9
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_message:
    type: string_textfield
    weight: 14
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_phone_number:
    type: phone_number_default
    weight: 10
    region: content
    settings:
      default_country: US
      placeholder: 'Phone number'
      phone_size: 60
      extension_size: 5
      country_selection: flag
    third_party_settings: {  }
  field_photo:
    type: image_image
    weight: 18
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_professional_field:
    type: entity_reference_autocomplete
    weight: 19
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_professional_level:
    type: entity_reference_autocomplete
    weight: 20
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_reference_number:
    type: string_textfield
    weight: 17
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_residence_place:
    type: string_textfield
    weight: 22
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_return_reason:
    type: string_textarea
    weight: 23
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_status:
    type: options_select
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 5
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  uid: true
