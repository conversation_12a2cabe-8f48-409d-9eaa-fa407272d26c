uuid: dc7d86ce-2036-4675-aa34-b36dc089bf6e
langcode: en
status: true
dependencies: {  }
id: job_application
title: 'Job application'
webform: job_application
content_type: null
field_title: null
use_encrypt: null
encryption_profile: null
redirect_to_entity: false
redirect_to_entity_message: ''
redirect_to_entity_message_on_update: '0'
sync_unique: false
sync_content: false
sync_content_delete: false
sync_content_field: field_webform_submission_id
sync_content_node_field: null
target_bundle: company-job_application
target_entity_type: group_relationship
elements:
  changed:
    type: true
    mapping: default_mapping
    webform_field: changed
    custom_check: 0
    custom_value: ''
  created:
    type: true
    mapping: default_mapping
    webform_field: created
    custom_check: 0
    custom_value: ''
  default_langcode:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '1'
  entity_id:
    type: true
    mapping: default_mapping
    webform_field: uid
    custom_check: 0
    custom_value: ''
  field_certificates:
    type: false
    mapping: default_mapping
    webform_field: certificates_existing
    custom_check: 0
    custom_value: ''
  field_certificates_new:
    type: false
    mapping: default_mapping
    webform_field: certificates_upload
    custom_check: 0
    custom_value: ''
  field_cv:
    type: false
    mapping: default_mapping
    webform_field: cv_existing
    custom_check: 0
    custom_value: ''
  field_cv_new:
    type: false
    mapping: default_mapping
    webform_field: cv_upload
    custom_check: 0
    custom_value: ''
  field_education:
    type: false
    mapping: entity_reference_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_education]'
  field_email:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:mail]'
  field_first_name:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_first_name]'
  field_interested_city:
    type: false
    mapping: entity_reference_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_interested_city]'
  field_job_post:
    type: true
    mapping: default_mapping
    webform_field: entity_id
    custom_check: 0
    custom_value: ''
  field_languages:
    type: false
    mapping: entity_reference_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_languages]'
  field_last_name:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_last_name]'
  field_linkedin_profile:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_linkedin]'
  field_message:
    type: false
    mapping: default_mapping
    webform_field: message
    custom_check: 0
    custom_value: ''
  field_phone_number:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_phone]'
  field_photo:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_photo:target_id]'
  field_professional_field:
    type: false
    mapping: entity_reference_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_professional_field]'
  field_professional_level:
    type: false
    mapping: entity_reference_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_professional_level]'
  field_reference_number:
    type: false
    mapping: default_mapping
    webform_field: reference_number
    custom_check: 0
    custom_value: ''
  field_residence_place:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_residence_place]'
  field_return_reason:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[current-user:talent:field_return_reason]'
  gid:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[webform_submission:source-entity-group-id]'
  group_type:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: company
  langcode:
    type: true
    mapping: default_mapping
    webform_field: langcode
    custom_check: 0
    custom_value: ''
  plugin_id:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: job_application
  type:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: company-job_application
  uid:
    type: true
    mapping: default_mapping
    webform_field: uid
    custom_check: 0
    custom_value: ''
