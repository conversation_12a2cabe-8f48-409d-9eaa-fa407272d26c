{#
/**
 * @file
 * Default theme implementation for profile documents component.
 *
 * Available variables:
 * - attributes: HTML attributes for the wrapper element.
 * - title_attributes: HTML attributes for the title element.
 * - title_classes: Array of CSS classes for the title.
 * - label: The label text to display.
 * - items: Array of document items.
 *   - attributes: HTML attributes for each item.
 *   - content: The document item content to display.
 */
#}
<div{{ attributes }}>
  <div class="heading-3 mb-6 flex justify-between">
    <div{{ title_attributes.addClass(title_classes) }}>{{ label }}</div>

    {% if with_documents_counter %}
      <div>{{ items|length }}/5</div>
    {% endif %}
  </div>
  <div class="flex flex-col gap-3">
  {% for item in items %}
    <div{{ item.attributes }}>{{ item.content }}</div>
  {% endfor %}
  </div>
</div>
