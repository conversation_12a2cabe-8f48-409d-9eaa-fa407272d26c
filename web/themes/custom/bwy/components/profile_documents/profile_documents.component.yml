name: Profile Documents
status: stable
props:
  type: object
  required:
    - attributes
    - title_attributes
    - label
    - items
  properties:
    attributes:
      type: object
    title_attributes:
      type: object
    title_classes:
      type: array
      items:
        type: string
    label:
      type: string
    items:
      type: array
      items:
        type: object
        properties:
          attributes:
            type: object
          content:
            type: string
  with_documents_counter:
    type: boolean
    required: false
    default: false
    description: Whether to show the documents counter
