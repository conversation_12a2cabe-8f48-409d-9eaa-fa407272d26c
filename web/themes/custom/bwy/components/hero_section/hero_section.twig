{# Hero Section Component #}
{# Set default values for visibility #}
{% set show_breadcrumbs = is_breadcrumbs is not defined or is_breadcrumbs %}
{% set show_title = is_title is not defined or is_title %}
{% set is_no_content = not show_title and not subtitle and not (cta.title is not empty and cta.url is not empty) %}
{% set images_path = file_url(directory ~ '/components/hero_section/images/') %}

{% set classes = [
  'hero',
  variant ? 'hero-' ~ variant : 'hero-default',
  'relative',
  'overflow-hidden',
  'bg-section-header',
  'text-background-main',
  'pt-124',
  'pb-34',
  'md:pt-67',
  'md:pb-60px'
] %}

<div {{ attributes.addClass(classes) }}>
  <div class="absolute top-0 right-0 overflow-hidden">
    {# Desktop image - hidden on mobile, visible from md up #}
    <img 
      class="hero-section__background tw-hidden md:block" 
      src="{{ images_path ~ (variant ? variant : 'default') ~ '.png' }}" 
      alt="{{ variant ? variant|capitalize ~ ' hero background' : 'Default hero background' }}"
      role="presentation"
      aria-hidden="true"
    >
    {# Mobile image - visible on mobile, hidden from md up #}
    <img 
      class="hero-section__background block md:hidden" 
      src="{{ images_path ~ (variant ? variant : 'default') ~ '-mobile.png' }}" 
      alt="{{ variant ? variant|capitalize ~ ' hero background' : 'Default hero background' }}"
      role="presentation"
      aria-hidden="true"
    >
  </div>

  <div class="hero--content-wrapper bwy-container relative z-10">
    {% if show_breadcrumbs %}
      <div class="mb-6">
        {{ drupal_block('system_breadcrumb_block', {label: false}) }}
      </div>
    {% endif %}
    {% if not is_no_content %}
      <div class="main-content">
        <div class="w-full md:w-content-7">
          {% if show_title or (subtitle is defined and subtitle) %} 
            <div class="flex flex-col gap-1 md:gap-2">
              {% if show_title %}
                {% include 'bwy:heading' with {
                  heading_html_tag: 'h1',
                  content: title ? title : node.label,
                } %}
              {% endif %}

              {% if subtitle is defined and subtitle %}
                {% include 'bwy:heading' with {
                  heading_html_tag: 'h2',
                  content: subtitle,
                } %}
              {% endif %}
            </div>
          {% endif %}

          {% if cta.title is not empty and cta.url is not empty %}
            <div class="mt-6">
              {% include '@bwy/components/button/button.twig' with {
                tag: 'a',
                text: cta.title,
                button_type: 'primary',
                button_attributes: create_attribute().setAttribute('href', cta.url)
              } %}
            </div>
          {% endif %}
        </div>
      </div>
    {% endif %}
  </div>
</div>
