{#
/**
 * @file
 * Template for a Event Card component.
 *
 * Available variables:
 * - image: Featured image for the event
 * - title: Event title
 * - date: Event start-end date
 * - place: Event place
 * - address: Event address
 * - location: Event location for the map icon
 * - url: URL to the full event page
 * - heading_level: Heading level for the title (default: 3)
 * - read_more_text: Text for read more link (default: 'Read more')
 * - category_text: Category badge text
 * - place_label: Label for the place
 * - address_label: Label for the address
 * - current_event: Whether the event is currently ongoing
 * - past_event: Whether the event has already ended
 */
#}

{%
  set card_classes = [
    'card',
    url ? 'card--hover',
    'event-card',
    'event-card--featured',
    'relative',
  ]
%}

{%
  set event_content_classes = [
    'h-full',
    'flex',
    'flex-col',
    'gap-2',
    'md:gap-4',
    'justify-between',
    'py-4',
    'px-3',
    'lg:py-12',
    'md:pl-8',
    'md:pr-6',
    'md:w-1/2',
  ]
%}

<div {{ attributes.addClass(card_classes) }}>
  {% if url %}
    {% include '@bwy/components/accessible_link/accessible_link.twig' with {
      url: url,
      title: title
    } %}
  {% endif %}
  <div class="md:flex md:items-center">
    {# Image section for featured #}
    {% if image %}
      <div class="event-image relative md:w-1/2">
        <div class="w-full overflow-hidden">
          {{ image }}
        </div>
      </div>
    {% endif %}
    <div{{ create_attribute().addClass(event_content_classes) }}>
        {# Category badge for featured #}
      <div class="category-badge mb-4 md:mb-0">
        <span class="inline-block text-black bg-red-400/35 link-text py-1 px-2 pr-5 rounded-l-lg [clip-path:polygon(0_0,100%_0,calc(100%-12px)_100%,0_100%)]">
          {{ category_text|default(current_event ? 'Last hours of the event'|t : 'Upcoming event'|t) }}
        </span>
      </div>

      {% include '@bwy/components/event_card_content_section/event_card_content_section.twig' with {
        title: title,
        date: date,
        place: place,
        address: address,
        location: location,
        heading_level: heading_level,
        place_label: place_label,
        address_label: address_label,
        no_place_address_text: no_place_address_text,
        past_event: past_event
      } %}

      {# Read more link #}
      {% if url %}
        <div class="event-read-more">
          {% include '@bwy/components/button/button.twig' with {
            text: read_more_text|default('Read more'|t),
            button_type: 'link',
            tag: 'a',
            with_icon: true,
            button_attributes: create_attribute().setAttribute('href', url)
          } %}
        </div>
      {% endif %}
    </div>
  </div>
</div>
