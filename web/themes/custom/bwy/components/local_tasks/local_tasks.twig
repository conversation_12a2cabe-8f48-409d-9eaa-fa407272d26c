{#
/**
 * @file
 * Theme implementation to display local tasks (tabs).
 *
 * Available variables:
 * - local_tasks: The local tasks block rendered through drupal_block().
 */
#}

{% set local_tasks = drupal_block('local_tasks_block') %}
{% if local_tasks.content['#primary'] is defined and local_tasks.content['#primary']|length > 0
  or local_tasks.content['#secondary'] is defined and local_tasks.content['#secondary']|length > 0 %}
  <div class="bwy-container my-8">
    {{ local_tasks|without('title') }}
  </div>
{% endif %}
