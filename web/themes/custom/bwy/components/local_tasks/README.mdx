# Local Tasks Component

The Local Tasks component is responsible for displaying <PERSON><PERSON><PERSON>'s local tasks (tabs) in a consistent manner across the site.

## Usage

```twig
{% include '@bwy/components/local_tasks/local_tasks.twig' %}
```

## Features

- Automatically fetches and displays local tasks using `drupal_block('local_tasks_block')`
- Handles both primary and secondary tabs
- Consistent styling with the site's design system
- Wrapped in a container with proper spacing
- Only renders if there are actual tabs to display

## Structure

The component consists of:
- `local_tasks.twig` - The template file
- `local_tasks.yml` - Component configuration
- `local_tasks.mdx` - This documentation file

## Examples

### Basic Usage
```twig
{# In your template file #}
{% include '@bwy/components/local_tasks/local_tasks.twig' %}
```

### Common Use Cases
- Node edit pages
- User profile pages
- Any content type that has additional tabs/actions

## Dependencies
- Requires <PERSON><PERSON><PERSON>'s local tasks block to be available
- Uses the `bwy-container` class for consistent spacing
