{#
/**
 * @file
 * Theme override for team_members view in unformatted format.
 *
 * Available variables:
 * - title: The title of this group of rows. May be empty.
 * - rows: A list of the view's row items.
 *   - attributes: The row's HTML attributes.
 *   - content: The row's content.
 * - view: The view object.
 * - default_row_class: A flag indicating whether default classes should be
 *   used on rows.
 *
 * @see template_preprocess_views_view_unformatted()
 */
#}

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-12">
  {% for row in rows %}
    {%
      set row_classes = [
        default_row_class ? 'views-row',
        'h-full'
      ]
    %}
    <div{{ row.attributes.addClass(row_classes) }}>
      {{ row.content }}
    </div>
  {% endfor %}
</div>
