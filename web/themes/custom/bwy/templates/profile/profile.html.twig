{#
/**
 * @file
 *
 * Default theme implementation for profiles.
 *
 * Available variables:
 * - content: Items for the content of the profile.
 *   Use 'content' to print them all, or print a subset such as
 *   'content.title'. Use the following code to exclude the
 *   printing of a given child element:
 *   @code
 *   {{ content|without('title') }}
 *   @endcode
 * - attributes: HTML attributes for the wrapper.
 * - view_mode: The profile view mode used.
 * - profile: The profile object.
 * - url: The profile URL, if available.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 *
 * @ingroup themeable
 */
#}

{% include '@bwy/components/profile_view_content/profile_view_content.twig' with {
  attributes: attributes,
  photo: content.field_photo,
  first_name: content.field_first_name,
  last_name: content.field_last_name,
  email: profile.getOwner.getEmail(),
  phone: content.field_phone,
  linkedin: content.field_linkedin,
  education: content.field_education,
  professional_field: content.field_professional_field,
  professional_level: content.field_professional_level,
  languages: content.field_languages,
  residence_place: content.field_residence_place,
  interested_city: content.field_interested_city,
  return_reason: content.field_return_reason,
  cv: content.field_cv,
  certificates: content.field_certificates
} %}
