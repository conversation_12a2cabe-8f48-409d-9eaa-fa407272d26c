{#
/**
 * @file
 * Theme override to display a news node in featured view mode.
 *
 * This template uses the news_card SDC component with the 'featured' variant
 * to render news articles in a hero-style layout.
 *
 * Available variables:
 * - node: The node entity with limited access to object properties and methods.
 * - label: The title of the node.
 * - content: All node items.
 * - url: Direct URL of the current node.
 * - date: Themed creation date field.
 * - view_mode: View mode; for example, "teaser" or "full".
 * - attributes: HTML attributes for the containing element.
 *
 * @see template_preprocess_node()
 */
#}

{# Include the news card component with featured variant #}

{% include 'bwy:news_card' with {
    variant: 'featured',
    slots: {
      image: content.field_image_media[0],
      title: node.label,
      summary: node.field_summary.value,
      date: node.getCreatedTime()|date('j M Y'),
      reading_time: node.node_read_time.value,
    },
    url: url,
    heading_level: 2,
  } only
%}
