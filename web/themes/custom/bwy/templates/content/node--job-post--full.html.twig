{#
/**
 * @file
 * Default theme implementation to display a node.
 *
 * Available variables:
 * - node: The node entity with limited access to object properties and methods.
 *   Only method names starting with "get", "has", or "is" and a few common
 *   methods such as "id", "label", and "bundle" are available. For example:
 *   - node.getCreatedTime() will return the node creation timestamp.
 *   - node.hasField('field_example') returns TRUE if the node bundle includes
 *     field_example. (This does not indicate the presence of a value in this
 *     field.)
 *   - node.isPublished() will return whether the node is published or not.
 *   Calling other methods, such as node.delete(), will result in an exception.
 *   See \Drupal\node\Entity\Node for a full list of public properties and
 *   methods for the node object.
 * - label: (optional) The title of the node.
 * - content: All node items. Use {{ content }} to print them all,
 *   or print a subset such as {{ content.field_example }}. Use
 *   {{ content|without('field_example') }} to temporarily suppress the printing
 *   of a given child element.
 * - author_picture: The node author user entity, rendered using the "compact"
 *   view mode.
 * - date: (optional) Themed creation date field.
 * - author_name: (optional) Themed author name field.
 * - url: Direct URL of the current node.
 * - display_submitted: Whether submission information should be displayed.
 * - attributes: HTML attributes for the containing element.
 *   The attributes.class element may contain one or more of the following
 *   classes:
 *   - node: The current template type (also known as a "theming hook").
 *   - node--type-[type]: The current node type. For example, if the node is an
 *     "Article" it would result in "node--type-article". Note that the machine
 *     name will often be in a short form of the human readable label.
 *   - node--view-mode-[view_mode]: The View Mode of the node; for example, a
 *     teaser would result in: "node--view-mode-teaser", and
 *     full: "node--view-mode-full".
 *   The following are controlled through the node publishing options.
 *   - node--promoted: Appears on nodes promoted to the front page.
 *   - node--sticky: Appears on nodes ordered above other non-sticky nodes in
 *     teaser listings.
 *   - node--unpublished: Appears on unpublished nodes visible only to site
 *     admins.
 * - title_attributes: Same as attributes, except applied to the main title
 *   tag that appears in the template.
 * - content_attributes: Same as attributes, except applied to the main
 *   content tag that appears in the template.
 * - author_attributes: Same as attributes, except applied to the author of
 *   the node tag that appears in the template.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 * - view_mode: View mode; for example, "teaser" or "full".
 * - page: Flag for the full page state. Will be true if view_mode is 'full'.
 *
 * @see template_preprocess_node()
 *
 * @ingroup themeable
 */
#}
{%
  set node_classes = [
    'node',
    node.isPromoted() ? 'node--promoted',
    node.isSticky() ? 'node--sticky',
    not node.isPublished() ? 'node--unpublished',
    node.bundle|clean_class,
    node.bundle|clean_class ~ '--' ~ view_mode|clean_class,
    'node--' ~ view_mode|clean_class,
    'node--' ~ node.bundle|clean_class ~ '--' ~ view_mode|clean_class,
    'view-mode--' ~ view_mode|clean_class,
  ]|merge(node_utility_classes ?: [])
%}

<article
  {{ node_attributes.addClass(node_classes) }}>
  {% block node_content %}
    {{ title_prefix }}
    {{ title_suffix }}

    {% if display_hero_section %}
      {% include '@bwy/components/hero_section/hero_section.twig' with {
        variant: hero_theme_variant|default('default'),
        is_title: false,
      } %}
    {% endif %}

    <div class="bwy-container relative mb-10 md:-mt-60px p-8 bg-background-main/75 backdrop-blur-100 border border-solid border-border-main rounded-lg">

      <div class="grid md:grid-cols-12 gap-8 md:gap-16">
        <div class="col-span-9">
          {% include '@bwy/components/local_tasks/local_tasks.twig' %}

          {% include '@bwy/components/job_post_content/job_post_content.twig' with {
            job_title: label,
            content: content,
            salary: salary_range,
          } %}
        </div>

        <div
          class="col-span-3">
          {{ content.entitygroupfield }}

          <div
            class="flex flex-col gap-2.5">
            <div>
              {% include '@bwy/components/button/button.twig' with {
                tag: 'a',
                text: 'Apply for the job'|t,
                button_type: 'primary',
                button_attributes: create_attribute().setAttribute('href', '#block-bwy-webform').addClass('w-full')
              } %}
            </div>
            {% if content.private_message_link is defined %}
              {{content.private_message_link}}
            {% elseif content.profile_is_private is defined and content.profile_is_private %}
              <a
                href="#"
                class="private_message_link"
                role="button">
                Send message
              </a>
              {% set banner = drupal_entity('webform', 'profile_visibility_banner_send_m') %}
              {{ banner|without('title') }}
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  {% endblock %}
</article>
