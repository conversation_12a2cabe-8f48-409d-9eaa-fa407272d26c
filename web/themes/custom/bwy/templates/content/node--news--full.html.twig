{#
/**
 * @file
 * Template for a Node.
 * Available variables:
  * - node: The node entity.
  * - label: The title of the node.
  * - author_name: The name of the author.
  * - author_picture: The picture of the author.
  * - date: The date of the node.
  * - content: All node items. Use {{ content }} to print them all,
  *   or print a subset such as {{ content.field_example }}. Use
  *   {{ content|without('field_example') }} to temporarily suppress the printing
  *   of a given child element.
  * - author: The author of the node.
  * - display_submitted: Whether submission information should be displayed.
  * - attributes: HTML attributes for the containing element.
  * - title_attributes: HTML attributes for the title.
  * - content_attributes: HTML attributes for the content.
  * - author_attributes: HTML attributes for the author.
  * - node_content_classes: A list of HTML classes for the node content.
  * - author_utility_classes: A list of HTML classes for the author.
  * - node_utility_classes: A list of HTML classes for the node.
  * - node_content_utility_classes: A list of HTML classes for the node content.
  * - view_mode: View mode; for example, 'full', 'teaser'.
  * - page: Flag for the full page state.
  * - title_prefix: Additional output populated by modules, intended to be displayed in front of the main title tag that appears in the template.
  * - title_suffix: Additional output populated by modules, intended to be displayed after the main title tag that appears in the template.
  *
 */
#}
{%
  set node_classes = [
    'node',
    node.isPromoted() ? 'node--promoted',
    node.isSticky() ? 'node--sticky',
    not node.isPublished() ? 'node--unpublished',
    node.bundle|clean_class,
    node.bundle|clean_class ~ '--' ~ view_mode|clean_class,
    'node--' ~ view_mode|clean_class,
    'node--' ~ node.bundle|clean_class ~ '--' ~ view_mode|clean_class,
    'view-mode--' ~ view_mode|clean_class,
  ]|merge(node_utility_classes ?: [])
%}

{%
  set node_content_classes = [
    'node__content',
    'md:max-w-250',
    'body-2',
  ]|merge(node_content_utility_classes ?: [])
%}

{% set node_attributes = attributes ?: create_attribute() %}

<article {{ node_attributes.addClass(node_classes) }}>
  {% block node_content %}

    {% if display_hero_section %}
      {% include '@bwy/components/hero_section/hero_section.twig' with {
        variant: hero_theme_variant|default('default'),
        is_title: false,
      } %}
    {% endif %}

    <div class="bwy-container mb-8 md:mb-15 md:-mt-60px">
      {%
        include 'bwy:news_card' with {
          variant: 'page',
          slots: {
            image: content.field_image_media[0],
            title: node.label,
            date: node.getCreatedTime()|date('j M Y'),
            reading_time: node.node_read_time.value,
          },
          heading_level: 1
        } only
      %}
    </div>

    {% include '@bwy/components/local_tasks/local_tasks.twig' %}

    <div class="bwy-container grid grid-cols-1 md:grid-cols-12 gap-5 md:gap-10">
      <aside class="md:col-span-2">
        <a href="/news" class="inline-block mb-3 py-2 md:py-0">
          {% include 'bwy:icon_with_text' with {
            icon_name: 'arrow_forward',
            text: 'Back to news'|t,
            classes: 'gap-1 body-3 text-text-main-lighter'
          } %}
        </a>

        {{ content.toc_js }}
      </aside>
      <main class="md:col-span-8">
        {{ content.field_formatted_description }}
      </main>
    </div>
    {% if content.field_gallery is not empty %}
      <div class="bwy-container">
        {{ content.field_gallery }}
      </div>
    {% endif %}
  {% endblock %}
</article>
