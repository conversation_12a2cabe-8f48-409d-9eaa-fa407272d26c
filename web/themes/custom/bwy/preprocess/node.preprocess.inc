<?php

/**
 * @file
 * Node preprocess functions.
 */

use <PERSON>upal\block\Entity\Block;
use Drupal\Core\Language\LanguageInterface;
use Drupal\views\Views;
use Drupal\paragraphs\Entity\Paragraph;

require_once dirname(__FILE__) . '/salary.inc';
require_once dirname(__FILE__) . '/date.inc';

/**
 * Implements hook_preprocess_node().
 */
function bwy_preprocess_node__news(&$variables) {
  if (in_array($variables['view_mode'], ['teaser', 'featured', 'full']) && !empty($variables['content']['field_image_media'][0])) {
    $variables['content']['field_image_media'][0]['#item_attributes']['class'][] = 'w-full';
    $variables['content']['field_image_media'][0]['#item_attributes']['class'][] = 'h-full';
    $variables['content']['field_image_media'][0]['#item_attributes']['class'][] = 'object-cover';
  }

  // Programmatically load and render the breadcrumb block for use in templates.
  $block = Block::load('system_breadcrumb_block');
  if ($block) {
    $view_builder = \Drupal::entityTypeManager()->getViewBuilder('block');
    $variables['breadcrumb_block'] = $view_builder->view($block);
  }
}

/**
 * Implements hook_preprocess_node().
 */
function bwy_preprocess_node__event(&$variables) {
  /** @var \Drupal\node\Entity\Node $node */
  $node = $variables['node'];

  if (in_array($variables['view_mode'], ['teaser', 'featured', 'full']) &&
      !empty($variables['content']['field_teaser_image'][0])) {
    $variables['content']['field_teaser_image'][0]['#item_attributes']['class'][] = 'w-full';
    $variables['content']['field_teaser_image'][0]['#item_attributes']['class'][] = 'h-full';
    $variables['content']['field_teaser_image'][0]['#item_attributes']['class'][] = 'object-cover';
  }

  // Get the referenced speaker entities.
  $variables['content']['speakers'] = [];
  if ($node->hasField('field_schedule') && !$node->get('field_schedule')->isEmpty()) {

    // Load the view and set the display.
    $view = Views::getView('speakers');
    if ($view && $view->access('default')) {

      $view->setDisplay('default');
      $view->preExecute();
      $view->execute();

      // Check if there are results.
      if (!empty($view->result)) {

        // Render the view.
        $rendered_view = $view->render();
        $variables['content']['speakers'] = $rendered_view;
      }
      else {

        // No results found.
        $variables['content']['speakers'] = NULL;
      }
    }
    else {

      // View does not exist or access denied.
      $variables['content']['speakers'] = NULL;
    }
  }
}

/**
 * Implements hook_preprocess_node().
 */
function bwy_preprocess_node(&$variables) {
  /** @var \Drupal\node\Entity\Node $node */
  $node = $variables['node'];
  $bundle = $node->bundle();
  $view_mode = $variables['view_mode'];

  switch ($bundle) {
    case 'landing_page':
      if ($view_mode === 'full') {
        // Get the hero theme value, default to 'default' if not set.
        $hero_theme_variant = 'default';
        $display_hero_section = FALSE;

        // Get the hero theme.
        if ($node->hasField('field_hero_theme') && !$node->get('field_hero_theme')->isEmpty()) {
          $hero_theme_variant = $node->get('field_hero_theme')->first()->getString();
          $display_hero_section = TRUE;
        }

        // Get subtitle if available.
        $hero_subtitle = '';
        if ($node->hasField('field_hero_subtitle') && !$node->get('field_hero_subtitle')->isEmpty()) {
          $hero_subtitle = $node->get('field_hero_subtitle')->value;
        }

        // Get button link and text if available.
        $hero_button_url = '';
        $hero_button_text = '';
        if ($node->hasField('field_hero_button') && !$node->get('field_hero_button')->isEmpty()) {
          $link_item = $node->get('field_hero_button')->first();
          if ($link_item) {
            $hero_button_url = $link_item->getUrl()->toString();
            $hero_button_text = $link_item->title;
          }
        }

        // Add to template variables.
        $variables['hero_theme_variant'] = $hero_theme_variant;
        $variables['display_hero_section'] = $display_hero_section;
        $variables['hero_subtitle'] = $hero_subtitle;
        $variables['hero_button_url'] = $hero_button_url;
        $variables['hero_button_text'] = $hero_button_text;

        // Define the allowed featured view + display combinations.
        $allowed_featured_blocks = [
          ['view_id' => 'news', 'display_id' => 'featured_news'],
          ['view_id' => 'events', 'display_id' => 'featured_event'],
        ];

        $variables['has_featured_block'] = FALSE;

        // Only proceed if the field exists and has values.
        if ($node->hasField('field_components') && !$node->get('field_components')->isEmpty()) {
          $paragraphs = $node->get('field_components')->referencedEntities();

          if (!empty($paragraphs)) {
            // Get the first paragraph entity.
            $first_paragraph = $paragraphs[0];

            // Check if it's a listings paragraph
            // and has the correct view reference.
            if ($first_paragraph instanceof Paragraph && $first_paragraph->bundle() === 'listings') {
              if (
                $first_paragraph->hasField('field_listing') &&
                !$first_paragraph->get('field_listing')->isEmpty()
              ) {
                $view_embed = $first_paragraph->get('field_listing')->first();
                $view_data = $view_embed->getValue();

                $view_id = $view_data['target_id'] ?? NULL;
                $display_id = $view_data['display_id'] ?? NULL;

                foreach ($allowed_featured_blocks as $allowed) {
                  if (
                    $view_id === $allowed['view_id'] &&
                    $display_id === $allowed['display_id']
                  ) {
                    // Found a featured block.
                    $variables['has_featured_block'] = TRUE;

                    // Render the first paragraph separately.
                    $view_builder = \Drupal::entityTypeManager()->getViewBuilder('paragraph');
                    $variables['featured_block'] = $view_builder->view($first_paragraph, 'default');

                    // Render the remaining paragraphs.
                    $remaining_paragraphs = array_slice($paragraphs, 1);
                    $variables['main_content'] = [];

                    foreach ($remaining_paragraphs as $paragraph) {
                      $variables['main_content'][] = $view_builder->view($paragraph, 'default');
                    }
                    break;
                  }
                }
              }
            }
          }
        }
      }
      break;

    case 'job_post':
      // Process salary information for job_post content type.
      if (
        $node->hasField('field_salary_from') &&
        $node->hasField('field_salary_to') &&
        $node->hasField('field_gross_net')
      ) {
        $variables['salary_range'] = _bwy_format_salary_range(
          $node,
          'field_salary_from',
          'field_salary_to',
          $node->get('field_gross_net')->value
        );
      }
      break;

    case 'event':
      /** @var \Drupal\address\Entity\Address $address */
      $address = $node->get('field_location')->first();
      if ($address) {
        $country_code = $address->getCountryCode();
        $langcode = \Drupal::languageManager()->getCurrentLanguage(LanguageInterface::TYPE_INTERFACE)->getId();
        $country = \Drupal::service('address.country_repository')->get($country_code, $langcode);
        $country_name = $country ? $country->getName() : '';

        $locality = $address->getLocality() ?? '';
        $location_string = $locality && $country_name
          ? $locality . ', ' . $country_name
          : ($locality ?: ($country_name ?: ''));

        $variables['content']['location_string'] = ['#markup' => $location_string];
      }

      // Use helper function to generate and
      // return a renderable formatted date for the event.
      $variables['content']['event_date'] = _bwy_get_formatted_event_date($node);
      break;
  }

  // Handle hero theme variants for content types.
  if ($view_mode === 'full') {
    $theme_variants = [
      'job_post' => 'blue',
      'news' => 'pink',
      'event' => 'red',
    ];
    if (isset($theme_variants[$bundle])) {
      $variables['hero_theme_variant'] = $theme_variants[$bundle];
      $variables['display_hero_section'] = TRUE;
    }
  }
}
