<?php

/**
 * @file
 * Preprocess logic for fields.
 */

use Drupal\Core\Url;

/**
 * Implements hook_preprocess_HOOK() for field.html.twig.
 */
function bwy_preprocess_field(array &$variables) {
  $element = $variables['element'];
  $field_name = $element['#field_name'];

  if ($field_name === 'field_linkedin') {
    // Extracts LinkedIn username from the URL and passes it to Twig.
    if (
      isset($variables['items'][0]['content']['#url']) &&
      $variables['items'][0]['content']['#url'] instanceof
    Url) {
      /** @var \Drupal\Core\Url $url */
      $url = $variables['items'][0]['content']['#url'];
      $url_string = $url->toString();

      $segments = array_filter(explode('/', trim($url_string, '/')));
      $username = end($segments);

      $variables['linkedin_username'] = $username;
    }
  }
}

/**
 * Implements hook_theme_suggestions_field_alter().
 */
function bwy_theme_suggestions_field_alter(array &$suggestions, array $variables) {
  // Fix theme suggestions for job application fields.
  if ($variables['element']['#entity_type'] === 'group_relationship' && $variables['element']['#bundle'] === 'company-job_application') {
    foreach ($suggestions as &$suggestion) {
      $suggestion = str_replace('-', '_', $suggestion);
    }
  }

  // Add theme suggestions for CV new and certificates new fields.
  if ($variables['element']['#field_name'] === 'field_certificates_new') {
    $suggestions[] = 'field__' . $variables['element']['#entity_type'] . '__field_certificates';
  }
  elseif ($variables['element']['#field_name'] === 'field_cv_new') {
    $suggestions[] = 'field__' . $variables['element']['#entity_type'] . '__field_cv';
  }
}
