<?php

/**
 * @file
 * Group relationship preprocess functions.
 */

/**
 * Implements hook_preprocess_HOOK().
 */
function bwy_preprocess_group_relationship(&$variables) {
  /** @var \Drupal\group\Entity\GroupRelationshipInterface $group_relationship */
  $group_relationship = $variables['group_relationship'];

  if ($group_relationship->bundle() === 'company-job_application') {
    $variables['#cache']['contexts'][] = 'user';

    // Add status update form for full view mode.
    if ($variables['view_mode'] === 'full') {
      $form_builder = \Drupal::formBuilder();
      $variables['status_update_form'] = $form_builder->getForm(
        'Drupal\bwy_job_application\Form\StatusUpdateForm',
        $group_relationship
      );
    }
  }
}
