/** @format */

(function ($, Drupal, drupalSettings, once) {
  "use strict";

  function countTotal(status, operation = "+") {
    status = (status + "").replace(" ", "");
    let selectorTotal = $(".status-" + status + " .total-status");
    let total = parseInt(selectorTotal.text()) || 0;
    if (operation === "+") {
      total += 1;
    } else {
      total -= 1;
    }
    selectorTotal.text(total);
    return total;
  }

  function countPoint(status, point, operation = "+") {
    status = (status + "").replace(" ", "");
    point = parseInt(point) || 0;
    let selectorTotal = $(".status-" + status + " .card-header .total .badge");
    let total = parseInt(selectorTotal.text()) || 0;
    if (operation === "+") {
      total += point;
    } else {
      total -= point;
    }
    selectorTotal.text(total);
    return total;
  }

  Drupal.behaviors.Kanban = {
    attach: function attach(context) {
      $(once("Kanban", ".views-view-kanban", context)).each(function () {
        let kanbanHeight = $(".views-view-kanban").height();
        if (kanbanHeight < 180) {
          kanbanHeight = 180 * 2;
        }
        $(".panel-body [droppable=true]").css(
          "min-height",
          kanbanHeight - 180 + "px"
        );
        draggableInit();
        // Detect variable to open
        let params = new window.URLSearchParams(window.location.search);
        if (params.get("kanbanTicket")) {
          $("#viewkanban" + params.get("kanbanTicket")).click();
        }
      });

      function draggableInit() {
        let dragData = {
          entityId: null,
          type: null,
          currentStatus: null,
          currentDrag: null,
          draggedElement: null,
          isDragging: false,
        };

        $(".views-view-kanban [draggable=true]").bind(
          "dragstart",
          function (event) {
            // Ensure we're dragging the card itself, not a child element
            let $draggedCard = $(this);

            // Prevent dragging if the target is a link or button inside the card
            if ($(event.target).is("a, button, .action-link, .action-link *")) {
              event.preventDefault();
              return false;
            }

            dragData.entityId = $draggedCard.data("id");
            dragData.type = $draggedCard.data("type");
            dragData.currentStatus = $draggedCard.data("value");
            dragData.currentDrag = $draggedCard.attr("id");
            dragData.draggedElement = $draggedCard;
            dragData.isDragging = true;

            // Set the drag data to the card's ID, not the event target
            event.originalEvent.dataTransfer.setData(
              "text/plain",
              $draggedCard.attr("id")
            );

            // Add visual feedback
            $draggedCard.addClass("dragging");
          }
        );

        // Handle drag end to restore counts if dropped in same column or cancelled
        $(".views-view-kanban [draggable=true]").bind(
          "dragend",
          function (event) {
            $(this).removeClass("dragging");
            // Reset drag data
            dragData.isDragging = false;
          }
        );

        $(".views-view-kanban .panel-body").bind("dragover", function (event) {
          let color = $(this).data("color");
          $(this).addClass(`bg-${color}-subtle`);
          event.preventDefault();
        });

        $(".views-view-kanban .panel-body").bind("dragleave", function () {
          let color = $(this).data("color");
          $(this).removeClass(`bg-${color}-subtle`);
        });

        $(".views-view-kanban [droppable=true]").bind("drop", function (event) {
          let $dropZone = $(this);
          let view_kanban = $dropZone.closest(".views-view-kanban");
          let view_id = view_kanban.data("view_id");
          let display_id = view_kanban.data("display_id");
          let stateValue = $dropZone.data("value");

          // Get the dragged element ID from the data transfer
          let elementId =
            event.originalEvent.dataTransfer.getData("text/plain");
          let $draggedElement = $("#" + elementId);

          // Remove visual feedback
          let color = $dropZone.parent().data("color");
          $dropZone.parent().removeClass(`bg-${color}-subtle`);

          // Check if we have valid drag data
          if (!dragData.isDragging || !dragData.entityId || !dragData.type) {
            event.preventDefault();
            return;
          }

          // If dropped in the same column, do nothing
          if (dragData.currentStatus === stateValue) {
            event.preventDefault();
            return;
          }

          let spinners =
            '<div class="spinners d-flex justify-content-center" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="' +
            Drupal.t("Loading") +
            "…" +
            '">' +
            '<div class="spinner-border" role="status"><span class="sr-only"></span></div>' +
            "</div>";

          $dropZone.prepend(spinners);

          // Update counts immediately for better UX
          countTotal(dragData.currentStatus, "-");
          countPoint(
            dragData.currentStatus,
            $draggedElement.data("point"),
            "-"
          );
          countTotal(stateValue, "+");
          countPoint(stateValue, $draggedElement.data("point"), "+");

          // Move the element
          $draggedElement.data("value", stateValue);
          $draggedElement.detach();
          $dropZone.prepend($draggedElement);

          // Generate URL for AJAX call
          let url =
            "views-kanban/update-state/" +
            view_id +
            "/" +
            display_id +
            "/" +
            dragData.entityId +
            "/" +
            stateValue;

          $.ajax({
            url: Drupal.url(url),
            success: function (result) {
              $dropZone.find(".spinners").remove();
              // Emit event viewKanban then another javascript that can catch it.
              const customEvent = new CustomEvent("viewsKanban", {
                detail: {
                  view_id: view_id,
                  display_id: display_id,
                  entityId: dragData.entityId,
                  state: dragData.currentStatus,
                  to: stateValue,
                  total: parseInt(
                    $(
                      ".status-" +
                        stateValue.replace(" ", "") +
                        " .total-status"
                    ).text()
                  ),
                  point: parseInt($draggedElement.data("point")) || 0,
                },
              });
              document.dispatchEvent(customEvent);
            },
            error: function (xhr, status, error) {
              // Revert the counts on error
              countTotal(stateValue, "-");
              countPoint(stateValue, $draggedElement.data("point"), "-");
              countTotal(dragData.currentStatus, "+");
              countPoint(
                dragData.currentStatus,
                $draggedElement.data("point"),
                "+"
              );

              // Move element back to original position
              $draggedElement.data("value", dragData.currentStatus);
              let $originalColumn = $(
                ".status-" +
                  dragData.currentStatus.replace(" ", "") +
                  " [droppable=true]"
              );
              $draggedElement.detach();
              $originalColumn.prepend($draggedElement);

              $dropZone.find(".spinners").remove();
              alert(Drupal.t("An error occurred during status update."));
            },
          });

          event.preventDefault();
        });
      }
    },
  };
})(jQuery, Drupal, drupalSettings, once);
