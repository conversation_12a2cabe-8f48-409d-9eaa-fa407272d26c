<?php

namespace Drupal\bwy_job_application\Form;

use <PERSON><PERSON>al\Core\Ajax\AjaxResponse;
use <PERSON>upal\Core\Ajax\MessageCommand;
use <PERSON><PERSON>al\Core\Ajax\ReplaceCommand;
use Drupal\Core\Entity\EntityFieldManagerInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Extension\ModuleHandlerInterface;
use Drupal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Messenger\MessengerInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\StringTranslation\StringTranslationTrait;
use Drupal\group\Entity\GroupRelationshipInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Form for updating job application status via AJAX.
 */
class StatusUpdateForm extends FormBase {

  use StringTranslationTrait;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * The messenger service.
   *
   * @var \Drupal\Core\Messenger\MessengerInterface
   */
  protected $messenger;

  /**
   * The module handler.
   *
   * @var \Drupal\Core\Extension\ModuleHandlerInterface
   */
  protected $moduleHandler;

  /**
   * The entity field manager.
   *
   * @var \Drupal\Core\Entity\EntityFieldManagerInterface
   */
  protected $entityFieldManager;

  /**
   * The group relationship entity.
   *
   * @var \Drupal\group\Entity\GroupRelationshipInterface
   */
  protected $groupRelationship;

  /**
   * Constructs a StatusUpdateForm object.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\Core\Messenger\MessengerInterface $messenger
   *   The messenger service.
   * @param \Drupal\Core\Extension\ModuleHandlerInterface $module_handler
   *   The module handler.
   * @param \Drupal\Core\Entity\EntityFieldManagerInterface $entity_field_manager
   *   The entity field manager.
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager, AccountInterface $current_user, MessengerInterface $messenger, ModuleHandlerInterface $module_handler, EntityFieldManagerInterface $entity_field_manager) {
    $this->entityTypeManager = $entity_type_manager;
    $this->currentUser = $current_user;
    $this->messenger = $messenger;
    $this->moduleHandler = $module_handler;
    $this->entityFieldManager = $entity_field_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('current_user'),
      $container->get('messenger'),
      $container->get('module_handler'),
      $container->get('entity_field.manager')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'bwy_job_application_status_update_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state, ?GroupRelationshipInterface $group_relationship = NULL) {
    if (!$group_relationship) {
      return $form;
    }

    $this->groupRelationship = $group_relationship;

    // Get the current status value.
    $current_status = $group_relationship->field_status->value;

    // Get allowed values from field definition.
    $field_definition = $group_relationship->getFieldDefinition('field_status');
    $allowed_values = $field_definition->getSetting('allowed_values');

    // The allowed values are stored as key => label pairs.
    $options = $allowed_values;

    $form['#prefix'] = '<div id="status-update-form-wrapper">';
    $form['#suffix'] = '</div>';

    $form['status'] = [
      '#type' => 'select',
      '#title' => $this->t('Status'),
      '#options' => $options,
      '#default_value' => $current_status,
      '#ajax' => [
        'callback' => '::ajaxUpdateStatus',
        'wrapper' => 'status-update-form-wrapper',
        'event' => 'change',
      ],
    ];

    $form['group_relationship_id'] = [
      '#type' => 'hidden',
      '#value' => $group_relationship->id(),
    ];

    return $form;
  }

  /**
   * AJAX callback for status update.
   */
  public function ajaxUpdateStatus(array &$form, FormStateInterface $form_state) {
    $response = new AjaxResponse();

    $new_status = $form_state->getValue('status');
    $group_relationship_id = $form_state->getValue('group_relationship_id');

    try {
      /** @var \Drupal\group\Entity\GroupRelationshipInterface $group_relationship */
      $group_relationship = $this->entityTypeManager
        ->getStorage('group_relationship')
        ->load($group_relationship_id);

      if (!$group_relationship) {
        throw new \Exception('Group relationship not found.');
      }

      // Check group permissions for job application relationships.
      $group = $group_relationship->getGroup();
      if (!$group || !$group->hasPermission('view job_application relationship', $this->currentUser)) {
        throw new \Exception('You do not have group permission to update job application status.');
      }

      $old_status = $group_relationship->field_status->value;

      // Update the status.
      $group_relationship->field_status->value = $new_status;

      // Allow other modules to alter the entity before saving.
      $context = NULL;
      $this->moduleHandler->alter('kanban_change_status', $group_relationship, $context, $old_status);

      $group_relationship->save();

      // Get status labels for the message.
      $field_definitions = $this->entityFieldManager->getFieldDefinitions('group_relationship', 'company-job_application');
      $field_definition = $field_definitions['field_status'] ?? NULL;
      $allowed_values = $field_definition ? $field_definition->getSetting('allowed_values') : [];

      // The allowed values are stored as key => label pairs.
      $old_label = $allowed_values[$old_status] ?? $old_status;
      $new_label = $allowed_values[$new_status] ?? $new_status;

      // Create success message.
      $message = $this->t('@user changed status from @old to @new', [
        '@user' => $this->currentUser->getDisplayName(),
        '@old' => $old_label,
        '@new' => $new_label,
      ]);

      // Add success message.
      $response->addCommand(new MessageCommand($message, NULL, ['type' => 'status']));

    }
    catch (\Exception $e) {
      // Add error message.
      $error_message = $this->t('An error occurred: @message', ['@message' => $e->getMessage()]);
      $response->addCommand(new MessageCommand($error_message, NULL, ['type' => 'error']));

      // Reset the form to original state.
      $form_state->setRebuild();
    }

    // Replace the form.
    $response->addCommand(new ReplaceCommand('#status-update-form-wrapper', $form));

    return $response;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    // This form is handled via AJAX, so this method is not used.
  }

}
